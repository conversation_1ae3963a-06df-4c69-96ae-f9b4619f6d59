from enum import Enum
from loguru import logger as logging

from mygpt import settings


class UserConfigType(str, Enum):
    MAX_TOKEN = (
        "max_token"  # 最大token数, 在设置此值后，用户订阅包最大token数以此值为准
    )
    MAX_QUESTION = (
        "max_question"  # 最大问题数, 在设置此值后，用户订阅包最大问题数以此值为准
    )
    MAX_UPLOAD_FILES = "max_upload_files"  # 最大上传文件数, 在设置此值后，用户订阅包最大上传文件数以此值为准
    SUPER_ADMIN = "super_admin"  # 系统超级管理员
    INVITE_NOTIFICATION = "invite_notification"  # 邀请通知
    ENABLE_DIGITAL_HUMAN_INTERFACE = (
        "enable_digital_human_interface"  # 启用数字人功能界面
    )


SUPER_USER_MANAGE_TYPE = [
    UserConfigType.MAX_TOKEN,
    UserConfigType.MAX_QUESTION,
    UserConfigType.MAX_UPLOAD_FILES,
    UserConfigType.SUPER_ADMIN,
    UserConfigType.ENABLE_DIGITAL_HUMAN_INTERFACE,
]


class FileType(str, Enum):
    AVATAR = "avatar"
    ORIGINAL = "original"
    FAQ_IMAGE = "faq_image"
    # 从文档或者网页中解析出来的图片
    PARSER_IMAGE = "parser_image"


class FileSource(str, Enum):
    ROBOT = "robot"
    DATASET = "dataset"


class VectorTextType(str, Enum):
    ORIGINAL = "original"
    EN = "en"
    UP_ORIGINAL = "up_original"
    UP_EN = "up_en"


class VectorStatus(str, Enum):
    INIT = "init"
    TRANSLATED = "tanslated"
    FINISHED = "finished"
    FAILED = "failed"


class VectorFileType(str, Enum):
    UPLOAD = "upload"
    CONVERT = "convert"  # not used
    HTML = "html"
    INTEGRATION = "integration"
    HTML_PDF = "html_pdf"
    SITEMAP = "sitemap"
    GITBOOK = "gitbook"
    FAQ = "faq"
    FAQ_QUESTION = "faq_question"


class IntegrationRuleType(str, Enum):
    # SIMPLE_URL = "simple_url"  # 未使用
    # GITBOOK = "gitbook"  # 未使用
    # SITEMAP = "sitemap"  # 未使用

    LARK = "lark"
    # GOOGLE_DRIVE = 'google_dri
    # ONE_DRIVE = 'one_drive'


class VectorFileSourceType(str, Enum):
    # 本地上传
    LOCAL_UPLOAD = "local_upload"

    # 基于规则从外部数据源同步
    # SIMPLE_URL = IntegrationRuleType.SIMPLE_URL.value
    # GITBOOK = IntegrationRuleType.GITBOOK.value
    # SITEMAP = IntegrationRuleType.SITEMAP.value

    LARK = IntegrationRuleType.LARK.value
    # GOOGLE_DRIVE = 'google_drive'
    # ONE_DRIVE = 'one_drive'


class DatasourceType(str, Enum):
    FIFE = "file"
    WEBSITE = "website"
    SITEMAP = "sitemap"
    GITBOOK = "gitbook"
    FAQ = "faq"
    API = "api"
    INTEGRATION = "integration"


class VectorFileStatus(str, Enum):
    CRAWLED = "crawled"  # 已抓取的网页
    PROCESS = "process"  # 正在处理的文件/网页
    COMPLETE = "complete"  # 处理完成的文件/网页
    FAIL = "fail"  # 处理失败
    Exceeded = "exceeded"  # 超过限制的文件/网页
    DELETED = "deleted"
    READY = "ready"  # 准备好的文件/网页


class AIType(str, Enum):
    DEFAULT = "default"  # 私有
    DEMO = "demo"  # 公开
    PUBLIC = "public"
    PRIVATE = "private"
    TRENDING = "trending"


class AIModel(str, Enum):
    FILE = "file"
    WEBSITE = "website"
    DIGITAL_HUMAN = "digital_human"
    AGENT = "agent"


class AIStatus(str, Enum):
    INIT = "init"
    READY = "ready"
    # 冻结
    FROZEN = "frozen"


class AIConfigType(str, Enum):
    SOURCE_LANG = "source_lang"
    TRANSLATE_QUESTION = "translate_question"
    FALLBACK_TO_CHATGPT = (
        "fallback_to_chatgpt"  # 无法根据相关知识回答时，让 LLM 用自己的知识回复
    )
    FRENQUENT_QUESTION = "frenquent_question"  # 常见问题
    WIDGET_CONFIGS = "widget_configs"
    VECTOR_STORAGE = "vector_storage"
    FAQ_VECTOR_STORAGE = "faq_vector_storage"
    EMBEDDINGS_MODEL = "embeddings_model"  # 词向量模型
    EMBEDDING_MODEL_NAME = "embedding_model_name"  # 向量模型名称
    EMBEDDING_DIMENSIONS = "embedding_dimensions"  # 向量维度
    LLM_PROVIDER = "llm_provider"  # LLM提供商
    MATCH_FAQ_MIN_SCORE = "match_faq_min_score"  # FAQ匹配最小分数
    FINAL_QUESTION_USE_GPT4 = "final_question_use_gpt4"
    OPENAI_MODEL = "openai_model"
    NOTIFICATION_ADMIN = "notification_admin"  # 打开小部件窗口通知管理员
    RECOMMEND = "recommend"
    CLIENT_INSTALLED = "client_installed"
    MAX_QUESTIONS = "max_questions"
    QUESTIONS_START_DATE = "questions_start_date"
    QUESTIONS_END_DATE = "questions_end_date"
    FAQ_SEARCH_WITH_ANSWER = "faq_search_with_answer"  # FAQ搜索时是否包含答案
    FAQ_SEARCH_ANSWER_WITH_EMBEDDING = (
        "faq_search_answer_with_embedding"  # FAQ搜索时是否包含词向量
    )
    ENV_CONTEXT = "env_context"  # 上下文
    ENABLE_IMAGES = "enable_images"  # 是否启用图片
    ENABLE_WEB_SEARCH = "enable_web_search"
    ENABLE_RERANK = "enable_rerank"
    ENABLE_OPENSEARCH = "enable_opensearch"
    WEB_SEARCH_SITE_LIST = "web_search_site_list"
    TALKING_STYLE = "talking_style"  # 对话风格
    CHAT_TEMPREATURE = "chat_temperature"  # 创造性，0-1
    UNKNOWN_TEXT = "unknown_text"  # 未知问题回答预设文本
    TRANSFER_TO_HUMAN = "transfer_to_human"  # 转人工
    # 用户设置的数字人prompt
    DIGITAL_HUMAN_PROMPT = "digital_human_prompt"
    ENABLE_ACCESS_CONTROL = "enable_access_control"
    ACCESS_CONTROL_ALLOW_EMAIL_LIST = "access_control_allow_email_list"
    # Bot 默认启用文本输入框 or 语音输入框
    INPUT_METHOD = "input_method"
    PREVIEW_PANEL_API = "preview_panel_api"
    ENABLE_TTS = "enable_tts"
    ENABLE_2D_DIGITAL_PERSON = "enable_2d_digital_person"
    MATCH_FAQ_OS_MIN_SCORE = "match_faq_opensearch_min_score"  # FAQ OS匹配最小分数


class DatasetConfigType(str, Enum):
    SOURCE_LANG = "source_lang"  # 数据源语言
    MATCH_FAQ_MIN_SCORE = "match_faq_min_score"  # FAQ匹配最小分数


class QuestionType(str, Enum):
    Generator = "generator"


class UserRole(str, Enum):
    SUPER_ADMIN = "super_admin"


class OpenaiApikeyStatus(str, Enum):
    NORMAl = "normal"
    INSUFFICIENT_BALANCE = "insufficient_balance"
    ERROR = "error"


class StripeModel(str, Enum):
    PAYMENT = "payment"
    SUBSCRIPTION = "subscription"


class ExcelTestTaskStatus(str, Enum):
    ACCEPTED = "accepted"
    PROCESSING = "processing"
    FINISHED = "finished"
    FAILED = "failed"
    CANCELED = "canceled"


class QuestionModel(str, Enum):
    DAVINCI003 = "davinci-003"
    TURBO16K = "turbo-16k"


class StreamingMessageDataFormat(str, Enum):
    PLAIN_TEXT = "PLAIN_TEXT"
    JSON_AST = "JSON_AST"
    OPENAI_AST = "OPENAI_AST"


class StreamingMessageDataType(str, Enum):
    REFERENCE_LIST = "REFERENCE_LIST"
    REFERENCE_SIGN = "REFERENCE_SIGN"
    LLM_CAN_ANSWER = "LLM_CAN_ANSWER"
    PLAIN_TEXT = "PLAIN_TEXT"
    FINISH = "FINISH"
    ERROR = "ERROR"
    RECOMMEND_LIST = "RECOMMEND_LIST"
    SHOW_REFERENCE_BUTTON = "SHOW_REFERENCE_BUTTON"
    FAQ_CHILD_NODE = "FAQ_CHILD_NODE"
    CLARIFICATION = "CLARIFICATION"
    TOOL_CALL = "TOOL_CALL"


class UserIntent(str, Enum):
    ASK_INFO_ABOUT_BOT = "ABOUT_BOT"
    ASK_INFO_ABOUT_COMPANY = "ABOUT_COMPANY"
    ASK_HUMAN_SUPPORT = "HUMAN_SUPPORT"


class VectorStorageType(str, Enum):
    PINECONE = "pinecone"
    QDRANT = "qdrant"
    QDRANT_ONE_COLLECTION = "qdrant_one_collection"


class EMBEDDINGS_MODEL(str, Enum):
    OPENAI = "openai"
    SENTENCE_TRANSFORMERS = "sentence_transformers"


class DATASET_STATUS(str, Enum):
    INIT = "init"
    READY = "ready"
    CLONING_TARGET = "cloning_target"  # 作为克隆的目标数据集
    CLONING_SOURCE = "cloning_source"  # 作为克隆的源数据集
    CLONING_FAILED = "cloning_failed"
    FROZEN = "frozen"  # 已冻结


class PROMPT_TYPE(str, Enum):
    INTENT_V1 = "intent_v1"
    QUERY_KEY_WORDS = "query_key_words"  # 提取提问关键词
    CHAT = "chat"
    LANGUAGE_DETECT = "language_detect"
    FAQ_ANSWER_TURBO = "faq_answer_turbo"
    FAQ_ASK_USER_TO_PROVIDE_MODEL_TURBO = "ask_user_to_provide_model_turbo"
    FAQ_ASK_FOR_MODEL_DETECT_TURBO = "faq_ask_for_model_detect_turbo"
    FAQ_MODEL_DETECT_TURBO = "faq_model_detect_turbo"
    FUNCTION_CALL = "function_call"
    FUNCTION_CALL_API = "function_call_api"
    SESSION_MESSAGE_TITLE = "session_message_title"


class STRIPE_ORDER(int, Enum):
    FREE = 0
    ENTERPRISE = 4


class LLM_PROVIDER(str, Enum):
    AZURE = "azure"
    OPENAI = "openai"
    AZURE_SYNC = "azure_sync"
    OPENAI_SYNC = "openai_sync"
    LOCAL_LLM = "local_llm"
    CLAUDE = "claude"
    GEMINI = "gemini"


class MESSAGE_COMES_FROM(str, Enum):
    FAQ = "faq"
    FUNCTION_CALL = "function_call"
    CHUNK = "chunk"
    GREETINGS = "greetings"
    AGENT = "agent"
    AGENT_TOOL = "agent_tool"
    AGENT_RAG = "agent_rag"
    AGENT_FAQ = "agent_faq"


class FaqSourceType(str, Enum):
    CONVERSATION = "conversation"
    USER_IMPORT = "user_import"
    USER_INPUT = "user_input"

    @classmethod
    def parse(cls, value: str, default: "FaqSourceType" = None) -> "FaqSourceType":
        """
        安全地解析源类型字符串为FaqSourceType枚举。
        如果解析失败，返回默认值或USER_IMPORT。

        :param value: 要解析的字符串
        :param default: 解析失败时返回的默认值
        :return: FaqSourceType枚举值
        """
        try:
            return cls(value)
        except ValueError:
            if default is None:
                return cls.USER_IMPORT
            return default

    def __str__(self):
        return self.value


class FAQ_TYPE(str, Enum):
    ANSWER = "answer"
    SOURCE = "source"


class MessagePageInfo(str, Enum):
    WIDGET = "widget"
    API = "api"
    PLAYGROUND = "playground"
    SHARE = "share"


class ResourceType(str, Enum):
    ROBOT = "robot"
    DATASET = "dataset"


class QdrantFaqCollectionName(str, Enum):
    FAQ = "gptbase_faq"
    FAQ_QUESTION = "gptbase_faq_question"


class OpenAIModel(str, Enum):
    TEXT_EMBEDDING_ADA_002 = "text-embedding-ada-002"
    TEXT_EMBEDDING_3_SMALL = "text-embedding-3-small"
    TEXT_EMBEDDING_3_LARGE = "text-embedding-3-large"
    GPT_4 = "gpt-4"  # 逐步替换
    GPT_35_TURBO = "gpt-3.5-turbo"
    GPT_4_TURBO_PREVIEW = "gpt-4-turbo-preview"
    # 以下为过时的模型，不再使用，不能删除，不能修改，否则历史数据查询出错
    GPT_35_TURBO_1106 = "gpt-3.5-turbo-1106"
    GPT_35_TURBO_16K = "gpt-3.5-turbo-16k"
    GPT_4_1106_PREVIEW = "gpt-4-1106-preview"
    # GPT_4_TURBO = "gpt-4-turbo"
    GPT_4_OMNI = "gpt-4o"
    GPT_4_OMNI_2024_05_13 = "gpt-4o-2024-05-13"
    GPT_4_OMNI_2024_08_06 = "gpt-4o-2024-08-06"
    GPT_4_OMNI_2024_11_20 = "gpt-4o-2024-11-20"
    GPT_4_OMNI_MINI = "gpt-4o-mini"
    O3_MINI = "o3-mini"
    O3_MINI_THINKING = "o3-mini-thinking"
    GEMINI_15_PRO_EXP_0801 = "gemini-1.5-pro-exp-0801"
    GEMINI_20_FLASH = "gemini-2.0-flash"
    GEMINI_20_FLASH_EXP_IMAGE_GENERATION = "gemini-2.0-flash-exp-image-generation"
    GEMINI_20_PRO_EXP_0205 = "gemini-2.0-pro-exp-02-05"
    GEMINI_25_PRO_EXP_0325 = "gemini-2.5-pro-exp-03-25"
    CLAUDE_35_SONNET_20240620 = "claude-3-5-sonnet-20240620"
    CLAUDE_35_SONNET_20241022 = "claude-3-5-sonnet-20241022"
    CLAUDE_35_SONNET = "claude-3-5-sonnet-20241022"
    CLAUDE_37_SONNET = "claude-3-7-sonnet-20250219"
    CLAUDE_37_SONNET_THINKING = "claude-3-7-sonnet-thinking"

    # 将参数传入模型转换
    @classmethod
    def to_model(cls, model: str):
        # 处理带有日期的模型名称
        normalized_model = "not a string"
        if isinstance(model, str):
            # 将原始模型名称转换为小写并替换连字符为下划线，便于匹配
            normalized_model = model.lower()
            
            # 检查前缀匹配
            if "gpt-4-turbo" in normalized_model or "gpt_4_turbo" in normalized_model:
                return cls.GPT_4_TURBO_PREVIEW
            elif ("gpt-4" in normalized_model or "gpt_4" in normalized_model) and not ("gpt-4o" in normalized_model or "gpt_4o" in normalized_model):
                return cls.GPT_4
            elif "gpt-3.5" in normalized_model or "gpt_35" in normalized_model:
                return cls.GPT_35_TURBO
            elif "gpt-4o" in normalized_model or "gpt_4o" in normalized_model:
                return cls.GPT_4_OMNI
            elif "o3-mini-thinking" in normalized_model or "o3_mini_thinking" in normalized_model:
                return cls.O3_MINI_THINKING
            elif "o3-mini" in normalized_model or "o3_mini" in normalized_model:
                return cls.O3_MINI
            elif "claude-3-7-sonnet-thinking" in normalized_model or "claude_37_sonnet_thinking" in normalized_model:
                return cls.CLAUDE_37_SONNET_THINKING
            elif "claude-3-7" in normalized_model or "claude_37" in normalized_model:
                return cls.CLAUDE_37_SONNET
            elif "claude-3-5" in normalized_model or "claude_35" in normalized_model:
                return cls.CLAUDE_35_SONNET
            elif "gemini-2.0-flash" in normalized_model or "gemini_20_flash" in normalized_model:
                return cls.GEMINI_20_FLASH
            
        # 兼容旧的精确匹配方式
        if model == "gpt_4_turbo":
            return cls.GPT_4_TURBO_PREVIEW
        elif model == "gpt_4":
            return cls.GPT_4
        elif model == "gpt_35_turbo":
            return cls.GPT_35_TURBO
        elif model == "gpt_4_omni":
            return cls.GPT_4_OMNI_2024_11_20
        elif model == "gpt_4o":
            return cls.GPT_4_OMNI_2024_11_20
        elif model == "o3_mini":
            return cls.O3_MINI
        elif model == "o3_mini_thinking":
            return cls.O3_MINI_THINKING
        elif model == "claude_35_sonnet":
            return cls.CLAUDE_35_SONNET
        elif model == "claude_37_sonnet":
            return cls.CLAUDE_37_SONNET
        elif model == "claude_37_sonnet_thinking":
            return cls.CLAUDE_37_SONNET_THINKING
        elif model == "gemini_20_flash":
            return cls.GEMINI_20_FLASH
        elif model == "":
            return cls.default()
        
        # 添加日志以便调试
        logging.warning(f"Model not found in to_model: {model}, normalized: {normalized_model}")
        raise ValueError(f"model {model} not found")

    @classmethod
    def default(cls):
        if settings.IS_USE_LOCAL_VLLM:
            return settings.LOCAL_VLLM_MODEL
        return OpenAIModel.to_model("claude_37_sonnet")


class FileStorage(str, Enum):
    S3 = "s3"
    LOCATION = "local"


class SessionMessageStatus(str, Enum):
    PENDING = "pending"
    FINISHED = "finished"


class RobotAccessType(str, Enum):
    PV = "pv"


class RobotType(str, Enum):
    RAG = "rag"
    DIGITAL_HUMAN = "digital_human"
    AGENT = "agent"


class AgentType(str, Enum):
    AGENTLY = "agently"
    REACT = "react"
    FUNCTIONCALL = "functioncall"


class FunctionCallStyle(str, Enum):
    OPENAI_STYLE = "openai_style"
    OPENAI_STYLE_OLD = "openai_style_old"
    CLAUDE_STYLE = "claude_style"


class IntegrationRuleFileListSyncingStatus(str, Enum):
    WAITING = "waiting"
    SYNCING = "syncing"
    FAILED = "failed"
    COMPLETED = "completed"


class IntegrationSyncOperation(str, Enum):
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"


class IntegrationSyncTriggerType(str, Enum):
    SCHEDULED = "scheduled"  # 定时触发
    MANUAL = "manual"  # 人工触发
